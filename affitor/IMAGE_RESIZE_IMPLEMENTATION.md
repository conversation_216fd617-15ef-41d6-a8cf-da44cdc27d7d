# Image Resizing Functionality for YooptaRichTextEditor

This document describes the implementation of image resizing functionality for the YooptaRichTextEditor component, allowing users to resize images with visual handles, aspect ratio controls, and dimension constraints.

## Overview

The image resizing system extends the existing Yoopta Image plugin with:
- **Visual resize handles**: Corner and edge handles for drag-to-resize interaction
- **Aspect ratio controls**: Lock/unlock functionality with UI controls
- **Real-time dimension display**: Live dimension display during resize operations
- **Dimension constraints**: Minimum/maximum size limits and responsive behavior
- **Persistence**: Custom dimensions are stored in the editor content structure

## Architecture

```
YooptaRichTextEditor → ResizableImagePlugin → ResizableImage Component
                    ↓
                Enhanced Upload Handler → Dimension Extraction → S3 Storage
                    ↓
                Content Structure with Dimensions → Persistence Layer
```

## Implementation Details

### 1. Core Components

#### ResizableImage Component (`src/components/ResizableImage.tsx`)
- **Purpose**: Wraps images with resize functionality using `re-resizable` library
- **Features**:
  - Drag-to-resize handles on all edges and corners
  - Aspect ratio lock/unlock toggle
  - Real-time dimension display
  - Reset to original size functionality
  - Responsive behavior for mobile devices
  - Read-only mode support

#### ResizableImagePlugin (`src/components/ResizableImagePlugin.tsx`)
- **Purpose**: Extends the Yoopta Image plugin with resize capabilities
- **Features**:
  - Custom render function for resizable images
  - Enhanced upload handler with dimension extraction
  - Proper data structure for storing dimensions
  - Integration with Yoopta's transform system

### 2. Key Features

#### Visual Resize Handles
- **Corner handles**: 8px circular handles for proportional resizing
- **Edge handles**: 20px×6px rectangular handles for single-axis resizing
- **Hover states**: Handles appear on hover and during selection
- **Mobile optimization**: Larger handles (12px) for touch interfaces

#### Aspect Ratio Controls
- **Lock/Unlock button**: Toggle aspect ratio preservation
- **Visual indicator**: Lock/unlock icon shows current state
- **Automatic calculation**: Uses natural image dimensions for ratio
- **Constraint application**: Maintains ratio during resize operations

#### Dimension Constraints
- **Minimum size**: 50px width and height
- **Maximum width**: 800px (configurable)
- **Container awareness**: Respects parent container boundaries
- **Responsive scaling**: Adjusts for different screen sizes

#### Real-time Dimension Display
- **Live dimensions**: Shows width×height during resize
- **Monospace display**: Uses consistent font for dimension text
- **Floating panel**: Positioned above image during interaction
- **Auto-hide**: Disappears when not resizing or hovering

### 3. Data Structure

Images are stored with the following enhanced structure:

```typescript
interface ResizableImageData {
  src: string;              // S3 URL
  alt?: string;             // Alt text
  width?: number;           // Display width
  height?: number;          // Display height
  naturalWidth?: number;    // Original image width
  naturalHeight?: number;   // Original image height
  aspectRatio?: number;     // Calculated aspect ratio
  sizes: {                  // Legacy compatibility
    width: number;
    height: number;
  };
}
```

### 4. Upload Enhancement

The enhanced upload handler:
1. Extracts natural dimensions before upload
2. Calculates reasonable display size (max 600px width)
3. Preserves aspect ratio
4. Stores both display and natural dimensions
5. Maintains compatibility with existing upload flow

## Usage

### For Users

1. **Insert Image**: Type `/image` in the editor and upload an image
2. **Resize Image**: 
   - Hover over the image to see resize handles
   - Drag corner handles for proportional resize
   - Drag edge handles for single-axis resize
   - Use control panel for aspect ratio and reset options
3. **Aspect Ratio**: Click lock/unlock button to toggle ratio preservation
4. **Reset Size**: Click reset button to return to original dimensions
5. **View Dimensions**: Dimensions are displayed during resize operations

### For Developers

#### Basic Integration
```typescript
import YooptaRichTextEditor from '@/components/YooptaRichTextEditor';

<YooptaRichTextEditor
  content={content}
  onChange={handleChange}
  pageId="your-page-id" // Required for image uploads
/>
```

#### Custom Configuration
```typescript
// The ResizableImagePlugin accepts the same options as the original Image plugin
const customImagePlugin = createResizableImagePlugin({
  async onUpload(file: File) {
    // Custom upload logic
    return await uploadToCustomStorage(file);
  },
});
```

## File Structure

```
src/
├── components/
│   ├── ResizableImage.tsx           # Core resizable image component
│   ├── ResizableImagePlugin.tsx     # Yoopta plugin extension
│   └── YooptaRichTextEditor.tsx     # Updated editor with resize support
├── styles/
│   ├── resizable-image.css          # Resize handle and control styles
│   └── yoopta-notion.css            # Updated integration styles
└── pages/
    └── test-resizable-image.tsx     # Test page for functionality
```

## Styling

### CSS Classes
- `.resizable-image-container`: Main container with hover states
- `.resize-handle`: Base handle styling
- `.resize-handle-corner`: Corner handle specific styles
- `.image-controls`: Control panel positioning
- `.control-button`: Button styling for controls
- `.dimension-display`: Dimension text styling

### Theme Integration
- Integrates with existing Notion-style theme
- Supports dark mode via CSS media queries
- Responsive design for mobile devices
- Consistent with Yoopta editor styling

## Browser Compatibility

- **Modern browsers**: Full functionality with all features
- **Mobile devices**: Touch-optimized handles and controls
- **Older browsers**: Graceful degradation to basic image display
- **Accessibility**: Keyboard navigation and screen reader support

## Performance Considerations

- **Lazy loading**: Images load only when needed
- **Debounced updates**: Resize events are throttled for performance
- **Memory management**: Proper cleanup of blob URLs and event listeners
- **Efficient rendering**: Uses React optimization patterns

## Testing

A test page is available at `/test-resizable-image` that demonstrates:
- Image upload and resize functionality
- All control features (lock/unlock, reset)
- Real-time dimension display
- Content persistence and JSON structure

## Future Enhancements

Potential improvements for future versions:
1. **Crop functionality**: Add image cropping capabilities
2. **Multiple formats**: Support for different image formats and sizes
3. **Batch operations**: Resize multiple images simultaneously
4. **Advanced constraints**: Custom aspect ratios and size presets
5. **Animation**: Smooth transitions during resize operations
6. **Accessibility**: Enhanced keyboard and screen reader support

## Troubleshooting

### Common Issues

1. **Handles not appearing**: Check CSS imports and hover states
2. **Resize not working**: Verify `re-resizable` dependency is available
3. **Dimensions not persisting**: Check editor onChange events
4. **Upload failures**: Verify pageId prop and upload configuration
5. **Styling conflicts**: Check CSS specificity and import order

### Debug Mode

Enable debug logging by checking browser console for:
- `[YooptaEditor]` prefixed messages
- Upload progress and dimension extraction
- Content validation and persistence events

## Dependencies

- `re-resizable`: ^6.9.11 (via @yoopta/image)
- `lucide-react`: ^0.485.0 (for control icons)
- `@yoopta/image`: ^4.9.9 (base image plugin)
- React 18+ and TypeScript support

import React from 'react';
import { Button, ButtonProps } from './button';
import { Loader2, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingButtonProps extends Omit<ButtonProps, 'children'> {
  children?: React.ReactNode;
  loading?: boolean;
  loadingText?: string;
  success?: boolean;
  successText?: string;
  successDuration?: number;
  icon?: React.ReactNode;
}

/**
 * Enhanced button component with loading and success states
 */
export const LoadingButton: React.FC<LoadingButtonProps> = ({
  children,
  loading = false,
  loadingText,
  success = false,
  successText,
  successDuration = 2000,
  icon,
  disabled,
  className,
  onClick,
  ...props
}) => {
  const [showSuccess, setShowSuccess] = React.useState(false);

  // Handle success state display
  React.useEffect(() => {
    if (success) {
      setShowSuccess(true);
      const timer = setTimeout(() => {
        setShowSuccess(false);
      }, successDuration);
      return () => clearTimeout(timer);
    }
  }, [success, successDuration]);

  const isDisabled = loading || disabled;
  const showSuccessState = showSuccess && !loading;

  const getButtonContent = () => {
    if (showSuccessState) {
      return (
        <>
          <Check className="mr-2 h-4 w-4" />
          {successText || children}
        </>
      );
    }

    if (loading) {
      return (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {loadingText || children}
        </>
      );
    }

    return (
      <>
        {icon && <span className="mr-2">{icon}</span>}
        {children}
      </>
    );
  };

  const getButtonVariant = () => {
    if (showSuccessState) {
      return 'default'; // Success state uses default variant with green styling
    }
    return props.variant || 'default';
  };

  return (
    <Button
      {...props}
      variant={getButtonVariant()}
      disabled={isDisabled}
      onClick={loading ? undefined : onClick}
      className={cn(
        showSuccessState && 'bg-green-600 hover:bg-green-700 text-white',
        className
      )}
    >
      {getButtonContent()}
    </Button>
  );
};

/**
 * Specialized save button with appropriate loading states
 */
export const SaveButton: React.FC<LoadingButtonProps & {
  autoSave?: boolean;
}> = ({ autoSave = false, loadingText, successText, ...props }) => {
  return (
    <LoadingButton
      loadingText={loadingText || (autoSave ? "Auto-saving..." : "Saving...")}
      successText={successText || (autoSave ? "Auto-saved" : "Saved")}
      successDuration={autoSave ? 1500 : 2000}
      {...props}
    />
  );
};

/**
 * Specialized publish button with appropriate loading states
 */
export const PublishButton: React.FC<LoadingButtonProps> = ({
  loadingText,
  successText,
  variant = "default",
  ...props
}) => {
  return (
    <LoadingButton
      loadingText={loadingText || "Publishing..."}
      successText={successText || "Published"}
      successDuration={3000}
      variant={variant}
      {...props}
    />
  );
};

/**
 * Auto-save indicator component
 */
export const AutoSaveIndicator: React.FC<{
  loading: boolean;
  success: boolean;
  error?: string | null;
  lastSaved?: string | null;
}> = ({ loading, success, error, lastSaved }) => {
  const [showSuccess, setShowSuccess] = React.useState(false);

  React.useEffect(() => {
    if (success) {
      setShowSuccess(true);
      const timer = setTimeout(() => {
        setShowSuccess(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  if (loading) {
    return (
      <div className="flex items-center text-sm text-muted-foreground">
        <Loader2 className="mr-1 h-3 w-3 animate-spin" />
        Auto-saving...
      </div>
    );
  }

  if (showSuccess) {
    return (
      <div className="flex items-center text-sm text-green-600">
        <Check className="mr-1 h-3 w-3" />
        Auto-saved
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center text-sm text-red-600">
        Auto-save failed
      </div>
    );
  }

  if (lastSaved) {
    const savedDate = new Date(lastSaved);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - savedDate.getTime()) / (1000 * 60));
    
    let timeText = '';
    if (diffMinutes < 1) {
      timeText = 'just now';
    } else if (diffMinutes < 60) {
      timeText = `${diffMinutes}m ago`;
    } else {
      const diffHours = Math.floor(diffMinutes / 60);
      timeText = `${diffHours}h ago`;
    }

    return (
      <div className="text-sm text-muted-foreground">
        Last saved {timeText}
      </div>
    );
  }

  return null;
};

import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useToast } from '@/contexts/ToastContext';

interface UseSuccessMessageOptions {
  message: string | null;
  clearAction: () => void;
  duration?: number;
  showToast?: boolean;
}

/**
 * Custom hook to handle success messages with auto-clear functionality
 * and optional toast notifications
 */
export const useSuccessMessage = ({
  message,
  clearAction,
  duration = 3000,
  showToast = true
}: UseSuccessMessageOptions) => {
  const dispatch = useDispatch();
  const { showToast: displayToast } = useToast();

  useEffect(() => {
    if (message) {
      // Show toast notification if enabled
      if (showToast) {
        displayToast(message, 'success');
      }

      // Auto-clear the message after the specified duration
      const timer = setTimeout(() => {
        dispatch(clearAction());
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [message, clearAction, dispatch, duration, showToast, displayToast]);

  return message;
};

/**
 * Hook specifically for auto-save success messages (shorter duration, no toast)
 */
export const useAutoSaveSuccessMessage = (
  message: string | null,
  clearAction: () => void
) => {
  return useSuccessMessage({
    message,
    clearAction,
    duration: 2000, // Shorter duration for auto-save
    showToast: false // No toast for auto-save to avoid spam
  });
};

/**
 * Hook for manual save success messages (with toast)
 */
export const useSaveSuccessMessage = (
  message: string | null,
  clearAction: () => void
) => {
  return useSuccessMessage({
    message,
    clearAction,
    duration: 3000,
    showToast: true
  });
};

/**
 * Hook for publish success messages (with toast)
 */
export const usePublishSuccessMessage = (
  message: string | null,
  clearAction: () => void
) => {
  return useSuccessMessage({
    message,
    clearAction,
    duration: 4000, // Longer duration for publish success
    showToast: true
  });
};
